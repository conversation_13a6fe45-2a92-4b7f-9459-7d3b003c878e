from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .services import AzureADService

class AzureCallbackView(APIView):
    """Exchanges authorization code for tokens and user info"""

    def post(self, request):
        # Extract code and redirect_uri from POST body
        code = request.data.get("code")
        redirect_uri = request.data.get("redirect_uri")
        print(f"Code: {code}, Redirect URI: {redirect_uri}")
        if not code or not redirect_uri:
            return Response({
                "success": False,
                "error": "Missing authorization code or redirect URI"
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Initialize service and exchange code for tokens
            azure_service = AzureADService()
            tokens = azure_service.exchange_code_for_tokens(code, redirect_uri)
            access_token = tokens.get('access_token')
            id_token = tokens.get('id_token')
            refresh_token = tokens.get('refresh_token')

            if not access_token or not id_token:
                return Response({
                    "success": False,
                    "error": "Failed to obtain tokens"
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get user profile
            user_profile = azure_service.get_user_profile(access_token, id_token)

            return Response({
                "success": True,
                "access_token": access_token,
                "refresh_token": refresh_token,
                "user": user_profile
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                "success": False,
                "error": f"Authorization error: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
