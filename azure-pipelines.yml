trigger:
- development-cicd

resources:
- repo: self

variables:
  imageRepo: space-optimization/optimization-backend-img
  tag: '$(Build.BuildId)'

stages:
- stage: Build
  displayName: Build image
  jobs:
  - job: Build
    displayName: Build
    pool:
      name: app-self-hosted
    steps:
    - task: Docker@2
      displayName: Build an image
      inputs:
        containerRegistry: 'ACR-DLL-PRD01-DLL04_02'
        repository: '$(imageRepo)'
        command: 'buildAndPush'
        Dockerfile: '$(Build.SourcesDirectory)/Dockerfile'
        tags: '$(tag)'
          
    - task: PublishPipelineArtifact@1
      inputs:
        targetPath: '$(Pipeline.Workspace)/s/manifests'
        artifact: 'manifests'
        publishLocation: 'pipeline'

- stage: Deploy
  displayName: Deploy to AKS Prod
  dependsOn: Build
  variables:
    acrsecret: k8sacrauth
    acrprodurl: 'lmapaz1acrdllprd02.azurecr.io'
  jobs:
  - job: Deploy
    displayName: Deploy to AKS
    pool:
      name: app-self-hosted
    steps:
    - task: DownloadPipelineArtifact@2
      inputs:
        buildType: 'current'
        artifactName: 'manifests'
        targetPath: '$(Pipeline.Workspace)/s/manifests'
    - task: KubernetesManifest@1
      inputs:
        action: 'createSecret'
        connectionType: 'azureResourceManager'
        azureSubscriptionConnection: 'SP-DLL-PRD01-DLL04'
        azureResourceGroup: 'RG_AZ1_PRD_DLL_ANL_02'
        kubernetesCluster: 'LMAPAZ1AKSPRDDLL03'
        useClusterAdmin: true
        namespace: 'dll-space-optimization'
        secretType: 'dockerRegistry'
        secretName: '$(acrsecret)'
        dockerRegistryEndpoint: 'ACR-DLL-PRD01-DLL04_02'
    - task: KubernetesManifest@1
      inputs:
        action: 'deploy'
        connectionType: 'azureResourceManager'
        azureSubscriptionConnection: 'SP-DLL-PRD01-DLL04'
        azureResourceGroup: 'RG_AZ1_PRD_DLL_ANL_02'
        kubernetesCluster: 'LMAPAZ1AKSPRDDLL03'
        useClusterAdmin: true
        namespace: 'dll-space-optimization'
        manifests: |
          $(Pipeline.Workspace)/s/manifests/space-optimization-deployment.yml
          $(Pipeline.Workspace)/s/manifests/space-optimization-configmap.yml
          $(Pipeline.Workspace)/s/manifests/space-optimization-service.yml
          $(Pipeline.Workspace)/s/manifests/space-optimization-secretprovider.yml
        containers: '$(acrprodurl)/$(imageRepo):$(tag)'
    
    - task: CmdLine@2
      displayName: Removing the image generated
      inputs:
        script: 'docker rmi -f $(acrprodurl)/$(imageRepo):$(tag)'