import requests
from django.conf import settings
import jwt
class AzureADConfig:
    """Azure AD Configuration"""
    def __init__(self):
        config = settings.AZURE_AD_CONFIG
        self.tenant_id = config['TENANT_ID']
        self.client_id = config['CLIENT_ID']
        self.client_secret = config['CLIENT_SECRET']
        self.redirect_uri = config['REDIRECT_URI']
        self.authority = config['AUTHORITY']
        self.scope = ' '.join(config['SCOPE'])
    
    @property
    def token_endpoint(self):
        return f"{self.authority}/{self.tenant_id}/oauth2/v2.0/token"
    
    @property
    def user_info_endpoint(self):
        return "https://graph.microsoft.com/v1.0/me"

class AzureADService:
    """Azure AD Service - All functionality in one class"""
    
    def __init__(self):
        self.config = AzureADConfig()
    
    def exchange_code_for_tokens(self, authorization_code, redirect_uri):
        """Exchange authorization code for tokens"""
        try:
            token_data = {
                'client_id': self.config.client_id,
                'client_secret': self.config.client_secret,
                'code': authorization_code,
                'redirect_uri': redirect_uri,
                'grant_type': 'authorization_code',
                'scope': self.config.scope
            }
                 
            response = requests.post(
                self.config.token_endpoint,
                data=token_data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=30
            )
    
            if response.status_code != 200:
                error_data = response.json()
                print(f"Error response: {error_data}")
                raise Exception(f"Token exchange failed: {error_data.get('error_description', 'Unknown error')}")
            
            token_response = response.json()

            return token_response
            
        except Exception as e:
            print(f"Exception in token exchange: {str(e)}")
            raise Exception(f"Token exchange error: {str(e)}")
    
    def get_user_profile(self, access_token, id_token):
        """Get user profile from both ID token and Graph API"""
        try:
            id_token_user = self._get_user_from_id_token(id_token)
            
            try:
                graph_user = self._get_user_from_graph(access_token)
                user_profile = {
                    'name': graph_user.get('name') or id_token_user.get('name'),
                    'email': graph_user.get('email') or id_token_user.get('email'),
                }
            except:
                user_profile = id_token_user
            
            return {k: v for k, v in user_profile.items() if v is not None}
            
        except Exception as e:
            raise Exception(f"User profile error: {str(e)}")
    
    def _get_user_from_id_token(self, id_token):
        """Extract user info from ID token"""
        try:
            decoded = jwt.get_unverified_claims(id_token)
            return {
                'name': decoded.get('name'),
                'email': decoded.get('email') or decoded.get('upn'),
                'display_name': decoded.get('name'),
                'object_id': decoded.get('oid')
            }
        except:
            return {}
    
    def _get_user_from_graph(self, access_token):
        """Fetch user from Microsoft Graph API"""
        try:
            headers = {'Authorization': f'Bearer {access_token}'}
            response = requests.get(self.config.user_info_endpoint, headers=headers, timeout=30)
            
            if response.status_code != 200:
                raise Exception("Graph API failed")
            
            user_data = response.json()
            return {
                'name': user_data.get('displayName'),
                'email': user_data.get('mail') or user_data.get('userPrincipalName'),
                'display_name': user_data.get('displayName'),
                'object_id': user_data.get('id')
            }
        except:
            raise Exception("Graph API error") 