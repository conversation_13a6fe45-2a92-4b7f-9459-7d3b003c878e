from rest_framework import serializers
from .models import ScenarioMetad, AppHbPreopt


class ScenarioMetadSerializer(serializers.ModelSerializer):
    class Meta:
        model = ScenarioMetad
        fields = '__all__'


class ClusterRequestSerializer(serializers.Serializer):
    concept = serializers.CharField()
    territory = serializers.CharField()
    loc_codes = serializers.ListField(
        child=serializers.CharField(), allow_empty=False
    )

class ClusterDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = None  # will be set dynamically
        fields = [
            "loc_cd", "loc_nm", "rgn_nm", "revenue", "units", "gmv",
            "total_lm", "total_customer", "total_invoice", "area_sqft",
            "cluster_num", "volume_contribution", "ethnicity_contribution",
            "revenue_per_sqft", "last_update_dt_tm"
        ]


class OutlierUpdateSerializer(serializers.Serializer):
    sub_clss_nm = serializers.Cha<PERSON><PERSON><PERSON>(max_length=100, required=True)
    loc_cd = serializers.CharField(max_length=20, required=True)
    outlier_status = serializers.CharField(max_length=20, required=True)
    outlier_status_final = serializers.CharField(max_length=20, required=True)


class OutlierResponseSerializer(serializers.ModelSerializer):
    class Meta:
        model = AppHbPreopt
        fields = [
            'sub_clss_nm', 'loc_cd', 'outlier_status',
            'lm_contribution_in_store', 'month', 'total_lm', 'gmv_per_day',
            'suggested_total_lm'
        ]
