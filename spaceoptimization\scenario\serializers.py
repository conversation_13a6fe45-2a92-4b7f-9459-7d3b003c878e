from rest_framework import serializers
from .models import ScenarioMetad, AppHbPreopt


class ScenarioMetadSerializer(serializers.ModelSerializer):
    class Meta:
        model = ScenarioMetad
        fields = '__all__'


class ClusterRequestSerializer(serializers.Serializer):
    concept = serializers.CharField()
    territory = serializers.CharField()
    loc_codes = serializers.ListField(
        child=serializers.CharField(), allow_empty=False
    )

class ClusterDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = None  # will be set dynamically
        fields = [
            "loc_cd", "loc_nm", "rgn_nm", "revenue", "units", "gmv",
            "total_lm", "total_customer", "total_invoice", "area_sqft",
            "cluster_num", "volume_contribution", "ethnicity_contribution",
            "revenue_per_sqft", "last_update_dt_tm"
        ]


class OutlierUpdateSerializer(serializers.Serializer):
    sub_clss_nm = serializers.Char<PERSON><PERSON>(max_length=100, required=True)
    loc_cd = serializers.CharField(max_length=20, required=True)
    outlier_status = serializers.CharField(max_length=20, required=True)
    outlier_status_final = serializers.CharField(max_length=20, required=True)

    def validate_loc_cd(self, value):
        """Validate that loc_cd is a valid format (assuming it should be numeric)"""
        try:
            int(value)
        except ValueError:
            raise serializers.ValidationError("loc_cd must be a valid number")
        return value

    def validate_outlier_status(self, value):
        """Validate outlier_status values"""
        valid_statuses = ['MAJOR_OUTLIER', 'MINOR_OUTLIER', 'NO_OUTLIER']
        if value not in valid_statuses:
            raise serializers.ValidationError(
                f"outlier_status must be one of: {', '.join(valid_statuses)}"
            )
        return value

    def validate_outlier_status_final(self, value):
        """Validate outlier_status_final values"""
        valid_final_statuses = ['APPROVED', 'REJECTED', 'PENDING']
        if value not in valid_final_statuses:
            raise serializers.ValidationError(
                f"outlier_status_final must be one of: {', '.join(valid_final_statuses)}"
            )
        return value


class OutlierResponseSerializer(serializers.ModelSerializer):
    class Meta:
        model = AppHbPreopt
        fields = [
            'sub_clss_nm', 'loc_cd', 'outlier_status', 'outlier_status_final',
            'lm_contribution_in_store', 'month', 'total_lm', 'gmv_per_day',
            'suggested_total_lm'
        ]
