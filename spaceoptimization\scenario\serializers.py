from rest_framework import serializers
from .models import ScenarioMetad


class ScenarioMetadSerializer(serializers.ModelSerializer):
    class Meta:
        model = ScenarioMetad
        fields = '__all__'


class ClusterRequestSerializer(serializers.Serializer):
    concept = serializers.CharField()
    territory = serializers.CharField()
    loc_codes = serializers.ListField(
        child=serializers.CharField(), allow_empty=False
    )

class ClusterDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = None  # will be set dynamically
        fields = [
            "loc_cd", "loc_nm", "rgn_nm", "revenue", "units", "gmv",
            "total_lm", "total_customer", "total_invoice", "area_sqft",
            "cluster_num", "volume_contribution", "ethnicity_contribution",
            "revenue_per_sqft", "last_update_dt_tm"
        ]
