apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: azure-space-optimization-secret-provider
spec:
  provider: azure
  parameters:
    usePodIdentity: "false"
    useVMManagedIdentity: "true"
    userAssignedIdentityID: "93734389-ee8e-4cbd-93b3-bbf4667b641f"
    keyvaultName: "LMAPAZ1KYVDLLPRD01"
    objects: |
      array:
        - |
          objectName: space-optimization-db-pwd
          objectType: secret
        - |
          objectName: space-optimization-ad-clientid
          objectType: secret
        - |
          objectName: space-optimization-ad-tenantid
          objectType: secret
        - |
          objectName: space-optimization-ad-clientsecret
          objectType: secret
    tenantId: "1f9b09b4-197c-4f1c-b0c5-571a6ccc96c8"
  secretObjects:
    - secretName: space-optimization-db-pwd
      type: Opaque
      data:
        - objectName: space-optimization-db-pwd
          key: space-optimization-db-pwd-key
    - secretName: space-optimization-ad-clientid
      type: Opaque
      data:
        - objectName: space-optimization-ad-clientid
          key: space-optimization-ad-clientid-key
    - secretName: space-optimization-ad-tenantid
      type: Opaque
      data:
        - objectName: space-optimization-ad-tenantid
          key: space-optimization-ad-tenantid-key
    - secretName: space-optimization-ad-clientsecret
      type: Opaque
      data:
        - objectName: space-optimization-ad-clientsecret
          key: space-optimization-ad-clientsecret-key
    