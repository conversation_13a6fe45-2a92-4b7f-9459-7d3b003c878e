from collections import defaultdict
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .serializers import (
    ScenarioMetadSerializer,
    ClusterDataSerializer,
    ClusterRequestSerializer,
    OutlierUpdateSerializer,
    OutlierResponseSerializer,
)
from .model_map import CLUSTER_MODEL_MAP
from .models import ScenarioStatus,AppHbPreopt
from .serializers import ScenarioMetadSerializer, ClusterDataSerializer
from django.utils import timezone
from django.db import transaction


class ScenarioCreateAPIView(APIView):


    def post(self, request):
        serializer = ScenarioMetadSerializer(data=request.data)
        try:
            with transaction.atomic():
                if serializer.is_valid(raise_exception=True):
                    scenario = serializer.save(
                        created_at=timezone.now(),
                        updated_at=timezone.now()
                    )
                    # Create scenario status
                    ScenarioStatus.objects.create(scenario=scenario)

                    return Response(
                        {'message': 'Scenario created successfully'},
                        status=status.HTTP_201_CREATED
                    )
        except Exception as e:
            # If any error occurs, transaction will rollback automatically
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class ClusterByLocationView(APIView):

    
    def post(self, request):
        # Step 1: Validate input
        input_serializer = ClusterRequestSerializer(data=request.data)
        if not input_serializer.is_valid():
            return Response(input_serializer.errors, status=400)

        validated = input_serializer.validated_data
        concept = validated["concept"]
        territory = validated["territory"]
        loc_codes = validated["loc_codes"]

        # Step 2: Resolve model from map
        table_name = f"de_{concept.lower()}_{territory.lower()}_cluster"
        ClusterModel = CLUSTER_MODEL_MAP.get(table_name)
        if not ClusterModel:
            return Response({"error": f"Unsupported cluster table: {table_name}"}, status=400)

        # Step 3: Fetch data
        try:
            filtered_data = ClusterModel.objects.filter(loc_cd__in=loc_codes)
        except Exception as e:
            return Response({"error": f"Database error: {str(e)}"}, status=500)

        # Step 4: Group by cluster_num
        grouped = defaultdict(list)
        for row in filtered_data:
            # Dynamically assign model to serializer
            ClusterDataSerializer.Meta.model = ClusterModel
            data = ClusterDataSerializer(row).data
            grouped[row.cluster_num].append(data)

        return Response(grouped)


class OutlierAPIView(APIView):
    def get(self, request):
        """
        Get outlier records with optional filtering by subclass and store
        Query parameters:
        - subclass: Filter by sub_clss_nm
        - store: Filter by loc_cd
        """
        try:
            # Get query parameters
            sub_clss_nm = request.query_params.get('subclass')
            loc_cd = request.query_params.get('store')

            # Build filters
            filters = {}
            if sub_clss_nm:
                filters['sub_clss_nm'] = sub_clss_nm
            if loc_cd:
                try:
                    filters['loc_cd'] = int(loc_cd)
                except ValueError:
                    return Response(
                        {
                            'error': 'Invalid store parameter',
                            'details': 'store parameter must be a valid number'
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # Query the database
            outliers = AppHbPreopt.objects.filter(**filters)

            # Check if any records found
            if not outliers.exists():
                return Response(
                    {
                        'message': 'No outlier records found',
                        'data': []
                    },
                    status=status.HTTP_200_OK
                )

            # Serialize the data using the response serializer
            serializer = OutlierResponseSerializer(outliers, many=True)

            return Response(
                {
                    'message': f'Found {outliers.count()} outlier record(s)',
                    'data': serializer.data
                },
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {
                    'error': 'Internal server error',
                    'details': str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def patch(self, request):
        """
        Update outlier status for a specific record based on sub_clss_nm and loc_cd
        """
        # Validate input data
        serializer = OutlierUpdateSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    'error': 'Invalid input data',
                    'details': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        validated_data = serializer.validated_data
        sub_clss_nm = validated_data['sub_clss_nm']
        loc_cd = validated_data['loc_cd']
        outlier_status = validated_data['outlier_status']
        outlier_status_final = validated_data['outlier_status_final']

        try:
            # Find the record to update
            outlier_record = AppHbPreopt.objects.get(
                sub_clss_nm=sub_clss_nm,
                loc_cd=int(loc_cd)  # Convert to int since model field is IntegerField
            )

            # Update the record
            outlier_record.outlier_status = outlier_status
            outlier_record.outlier_status_final = outlier_status_final
            outlier_record.save(update_fields=['outlier_status', 'outlier_status_final'])

            # Serialize the updated record for response
            response_serializer = OutlierResponseSerializer(outlier_record)

            return Response(
                {
                    'message': 'Outlier record updated successfully',
                    'data': response_serializer.data
                },
                status=status.HTTP_200_OK
            )

        except AppHbPreopt.DoesNotExist:
            return Response(
                {
                    'error': 'Record not found',
                    'details': f'No record found with sub_clss_nm="{sub_clss_nm}" and loc_cd="{loc_cd}"'
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except ValueError as e:
            return Response(
                {
                    'error': 'Invalid data format',
                    'details': f'loc_cd must be a valid integer: {str(e)}'
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {
                    'error': 'Internal server error',
                    'details': str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
