from collections import defaultdict
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .serializers import (
    ScenarioMetadSerializer, 
    ClusterDataSerializer, 
    ClusterRequestSerializer,
)
from .model_map import CLUSTER_MODEL_MAP
from .models import ScenarioStatus,AppHbPreopt
from .serializers import ScenarioMetadSerializer, ClusterDataSerializer
from django.utils import timezone
from django.db import transaction


class ScenarioCreateAPIView(APIView):


    def post(self, request):
        serializer = ScenarioMetadSerializer(data=request.data)
        try:
            with transaction.atomic():
                if serializer.is_valid(raise_exception=True):
                    scenario = serializer.save(
                        created_at=timezone.now(),
                        updated_at=timezone.now()
                    )
                    # Create scenario status
                    ScenarioStatus.objects.create(scenario=scenario)

                    return Response(
                        {'message': 'Scenario created successfully'},
                        status=status.HTTP_201_CREATED
                    )
        except Exception as e:
            # If any error occurs, transaction will rollback automatically
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class ClusterByLocationView(APIView):

    
    def post(self, request):
        # Step 1: Validate input
        input_serializer = ClusterRequestSerializer(data=request.data)
        if not input_serializer.is_valid():
            return Response(input_serializer.errors, status=400)

        validated = input_serializer.validated_data
        concept = validated["concept"]
        territory = validated["territory"]
        loc_codes = validated["loc_codes"]

        # Step 2: Resolve model from map
        table_name = f"de_{concept.lower()}_{territory.lower()}_cluster"
        ClusterModel = CLUSTER_MODEL_MAP.get(table_name)
        if not ClusterModel:
            return Response({"error": f"Unsupported cluster table: {table_name}"}, status=400)

        # Step 3: Fetch data
        try:
            filtered_data = ClusterModel.objects.filter(loc_cd__in=loc_codes)
        except Exception as e:
            return Response({"error": f"Database error: {str(e)}"}, status=500)

        # Step 4: Group by cluster_num
        grouped = defaultdict(list)
        for row in filtered_data:
            # Dynamically assign model to serializer
            ClusterDataSerializer.Meta.model = ClusterModel
            data = ClusterDataSerializer(row).data
            grouped[row.cluster_num].append(data)

        return Response(grouped)


class OutlierAPIView(APIView):
    def get(self, request):
        sub_clss_nm = request.query_params.get('subclass')
        loc_cd = request.query_params.get('store')
        filters = {}
        if sub_clss_nm:
            filters['sub_clss_nm'] = sub_clss_nm
        if loc_cd:
            filters['loc_cd'] = loc_cd
        outliers = AppHbPreopt.objects.filter(**filters)
        # Only return the required fields
        data = outliers.values(
            'lm_contribution_in_store',  # LM_CONTRIBUTION_IN_STORE
            'sub_clss_nm',               # SUB_CLSS_NM
            'loc_cd',                    # LOC_CD
            'month',                     # MONTH
            'total_lm',                  # TOTAL_LM
            'gmv_per_day',               # GMV_PER_DAY
            'suggested_total_lm',        # SUGGESTED_TOTAL_LM
            'outlier_status'             # OUTLIER_STATUS
        )
        return Response(list(data))

    def put(self, request):
        pass
