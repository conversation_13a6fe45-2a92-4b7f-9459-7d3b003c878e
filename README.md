# Space Optimization Backend

## Development Setup

### Prerequisites
- Python 3.10
- uv (Python package installer)

### Installation

1. Install uv:
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

2. Create and activate a new virtual environment:
```bash
uv venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

3. Install dependencies:
```bash
uv pip install -e ".[dev]"
```

### Running the Application

1. Start the development server:
```bash
python manage.py runserver
```

2. For production:
```bash
./run-django-production.sh
```

## Docker

Build and run the Docker container:
```bash
docker build -t space-optimization .
docker run -p 8000:8000 space-optimization
```

## Project Structure

- `azureauth/` - Azure authentication related code
- `manifests/` - Kubernetes manifests
- `pyproject.toml` - Project dependencies and metadata
- `Dockerfile` - Container configuration

# Introduction 
TODO: Give a short introduction of your project. Let this section explain the objectives or the motivation behind this project. 

# Getting Started
TODO: Guide users through getting your code up and running on their own system. In this section you can talk about:
1.	Installation process
2.	Software dependencies
3.	Latest releases
4.	API references

# Build and Test
TODO: Describe and show how to build your code and run the tests. 

# Contribute
TODO: Explain how other users and developers can contribute to make your code better. 

If you want to learn more about creating good readme files then refer the following [guidelines](https://docs.microsoft.com/en-us/azure/devops/repos/git/create-a-readme?view=azure-devops). You can also seek inspiration from the below readme files:
- [ASP.NET Core](https://github.com/aspnet/Home)
- [Visual Studio Code](https://github.com/Microsoft/vscode)
- [Chakra Core](https://github.com/Microsoft/ChakraCore)