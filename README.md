# Space Optimization Backend

## Development Setup

### Prerequisites
- Python 3.10
- uv (Python package installer)
- MySQL database (local installation or Docker)

### Installation

1. Install uv:
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

2. Create and activate a new virtual environment:
```bash
uv venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

3. Install dependencies:
```bash
uv pip install -e ".[dev]"
```

4. Set up environment variables:
```bash
# Copy the example environment file
cp .env.example spaceoptimization/spaceoptimization/.env

# Edit the .env file with your local configuration
# Update database credentials, Azure AD settings, etc.
```

5. Set up the database:
```bash
# Navigate to the Django project directory
cd spaceoptimization

# Run migrations
uv run manage.py makemigrations
uv run manage.py migrate

# Create a superuser (optional)
uv run manage.py createsuperuser
```

### Running the Application

1. Start the development server:
```bash
cd spaceoptimization
uv run manage.py runserver
```

The application will be available at `http://localhost:8000`

2. For production:
```bash
./run-django-production.sh
```

### Environment Variables

The application uses the following environment variables for configuration:

#### Required for Local Development:
- `DEBUG`: Set to `True` for development
- `SECRET_KEY`: Django secret key
- `DB_NAME`: Database name
- `DB_USER`: Database username
- `DB_PASSWORD`: Database password
- `DB_HOST`: Database host (usually `localhost` for local development)
- `DB_PORT`: Database port (usually `3306` for MySQL)

#### Azure AD Configuration:
- `AZURE_AD_CLIENT_ID`: Azure AD application client ID
- `AZURE_AD_CLIENT_SECRET`: Azure AD application client secret
- `AZURE_AD_TENANT_ID`: Azure AD tenant ID
- `AZURE_AD_REDIRECT_URI`: Redirect URI for OAuth flow
- `AZURE_AD_AUTHORITY`: Azure AD authority URL
- `AZURE_AD_SCOPE`: Comma-separated list of OAuth scopes

#### Optional:
- `CORS_ALLOWED_ORIGINS`: Comma-separated list of allowed CORS origins for frontend development

## Docker

### Production Docker Build
Build and run the Docker container:
```bash
docker build -t space-optimization .
docker run -p 8000:8000 space-optimization
```

### Local Development with Docker Compose
For local development with a MySQL database:

1. Copy environment variables:
```bash
cp .env.example .env.local
# Edit .env.local with your Azure AD credentials
```

2. Start the services:
```bash
docker-compose -f docker-compose.dev.yml --env-file .env.local up -d
```

This will start:
- MySQL database on port 3306
- Django backend on port 8000

3. Stop the services:
```bash
docker-compose -f docker-compose.dev.yml down
```

4. To remove volumes (database data):
```bash
docker-compose -f docker-compose.dev.yml down -v
```

## Project Structure

- `azureauth/` - Azure authentication related code
- `manifests/` - Kubernetes manifests
- `pyproject.toml` - Project dependencies and metadata
- `Dockerfile` - Container configuration

# Introduction 
TODO: Give a short introduction of your project. Let this section explain the objectives or the motivation behind this project. 

# Getting Started
TODO: Guide users through getting your code up and running on their own system. In this section you can talk about:
1.	Installation process
2.	Software dependencies
3.	Latest releases
4.	API references

# Build and Test
TODO: Describe and show how to build your code and run the tests. 

# Contribute
TODO: Explain how other users and developers can contribute to make your code better. 

If you want to learn more about creating good readme files then refer the following [guidelines](https://docs.microsoft.com/en-us/azure/devops/repos/git/create-a-readme?view=azure-devops). You can also seek inspiration from the below readme files:
- [ASP.NET Core](https://github.com/aspnet/Home)
- [Visual Studio Code](https://github.com/Microsoft/vscode)
- [Chakra Core](https://github.com/Microsoft/ChakraCore)