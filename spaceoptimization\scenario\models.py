from django.db import models

class ScenarioMetad(models.Model):
    name = models.Char<PERSON>ield(max_length=128)
    user_id = models.IntegerField()
    season_type = models.CharField(max_length=32)
    eval_type = models.CharField(max_length=32)
    event_name = models.CharField(max_length=128)
    eval_start = models.DateTimeField()
    eval_end = models.DateTimeField()
    ref_start = models.DateTimeField()
    ref_end = models.DateTimeField()
    CNCPT_NM = models.CharField(max_length=64)
    TERRITORY_NM = models.CharField(max_length=64)
    metric = models.CharField(max_length=64)
    sqft_file_id = models.IntegerField(null=True, blank=True)
    mdq_file_id = models.IntegerField(null=True, blank=True)
    cover_file_id = models.IntegerField(null=True, blank=True)
    exclusion_file_id = models.IntegerField(null=True, blank=True)
    loc_cd = models.JSO<PERSON>ield()
    created_by = models.IntegerField()
    updated_by = models.IntegerField()
    created_at = models.DateTimeField()
    updated_at = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'SCENARIO_METAD'


class ScenarioStatus(models.Model):
    scenario = models.ForeignKey(ScenarioMetad, on_delete=models.DO_NOTHING, db_column='scenario_id')
    status = models.CharField(max_length=64, default='CREATED')

    class Meta:
        managed = False
        db_table = 'SCENARIO_STATUS'


class BaseDeClusterModel(models.Model):
    loc_cd = models.CharField(max_length=20)
    loc_nm = models.CharField(max_length=100)
    rgn_nm = models.CharField(max_length=100)
    revenue = models.DecimalField(max_digits=32, decimal_places=1)
    units = models.DecimalField(max_digits=32, decimal_places=1)
    gmv = models.DecimalField(max_digits=32, decimal_places=1)
    total_lm = models.DecimalField(max_digits=32, decimal_places=1)
    total_customer = models.BigIntegerField()
    total_invoice = models.BigIntegerField()
    area_sqft = models.DecimalField(max_digits=32, decimal_places=1)
    cluster_num = models.IntegerField()
    volume_contribution = models.JSONField()
    ethnicity_contribution = models.JSONField()
    revenue_per_sqft = models.DecimalField(max_digits=32, decimal_places=1)
    last_update_dt_tm = models.DateTimeField()

    class Meta:
        abstract = True


class DeHbAeCluster(BaseDeClusterModel):
    class Meta:
        managed = False  # Don't let Django try to create or modify this table
        db_table = 'de_hb_ae_cluster'


class DeLsKwCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_kw_cluster'


class DeSpEgCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_eg_cluster'


class BaseAppClusterModel(models.Model):
    scenario_id = models.IntegerField()
    territory_nm = models.CharField(max_length=64)
    loc_cd = models.CharField(max_length=32)
    loc_nm = models.CharField(max_length=128)
    stnd_trrtry_nm = models.CharField(max_length=64)
    rgn_nm = models.CharField(max_length=64)

    revenue = models.DecimalField(max_digits=32, decimal_places=1)
    units = models.DecimalField(max_digits=32, decimal_places=1)
    gmv = models.DecimalField(max_digits=32, decimal_places=1)
    total_lm = models.DecimalField(max_digits=32, decimal_places=1)
    total_customer = models.BigIntegerField()
    total_invoice = models.BigIntegerField()
    area_sqft = models.DecimalField(max_digits=32, decimal_places=1)

    cluster_num = models.IntegerField()
    volume_contribution = models.JSONField()
    ethnicity_contribution = models.JSONField()
    revenue_per_sqft = models.DecimalField(max_digits=32, decimal_places=1)

    updated_at = models.DateTimeField()
    updated_by = models.IntegerField()
    new_cluster_num = models.IntegerField()

    class Meta:
        abstract = True


class AppLsStrCluster(BaseAppClusterModel):
    class Meta:
        managed = False
        db_table = 'app_ls_str_cluster'


class AppHbStrCluster(BaseAppClusterModel):
    class Meta:
        managed = False
        db_table = 'app_hb_str_cluster'


class AppSpStrCluster(BaseAppClusterModel):
    class Meta:
        managed = False
        db_table = 'app_sp_str_cluster'


class AppHbPreopt(models.Model):
    territory_nm = models.CharField(max_length=20)
    loc_cd = models.IntegerField()
    loc_nm = models.CharField(max_length=100)
    stnd_trrtry_nm = models.CharField(max_length=100)
    rgn_nm = models.CharField(max_length=100)
    grp_nm = models.CharField(max_length=100)
    dpt_nm = models.CharField(max_length=100)
    clss_nm = models.CharField(max_length=100)
    sub_clss_nm = models.CharField(max_length=100)
    month = models.IntegerField() 

    mnth_avg_soh = models.FloatField()
    mnth_avg_itm_cnt = models.FloatField()
    mnth_avg_optn_cnt = models.FloatField()
    mnth_end_soh = models.FloatField()
    mnth_end_itm_cnt = models.IntegerField()
    mnth_end_optn_cnt = models.IntegerField()

    net_sls_amt = models.FloatField()
    rtl_qty = models.FloatField()
    gmv = models.FloatField()
    inv_cnt = models.IntegerField()
    cust_cnt = models.IntegerField()
    str_visits = models.IntegerField()
    str_cust_cnt = models.IntegerField()

    cust_pen = models.FloatField()
    spc = models.FloatField()
    margin_perc = models.FloatField()
    asp = models.FloatField()
    sls_per_inv = models.FloatField()
    units_per_inv = models.FloatField()
    ros = models.FloatField()
    cover = models.FloatField()

    total_lm = models.FloatField()
    gmv_per_day = models.FloatField()
    gmv_per_lm = models.FloatField()
    lm_contribution_in_store = models.FloatField()

    outlier_status = models.CharField(max_length=20)
    suggested_total_lm = models.FloatField()
    last_update_dt_tm = models.DateTimeField()
    outlier_status_final = models.CharField(max_length=20,null=True, blank=True)

    class Meta:
        # abstract = True
        db_table = 'app_hb_preopt'


# # Concept: LS
# class DeLsAePreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_ls_ae_preopt'

# class DeLsBhPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_ls_bh_preopt'

# class DeLsEgPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_ls_eg_preopt'

# class DeLsKsPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_ls_ks_preopt'

# class DeLsKwPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_ls_kw_preopt'

# class DeLsOmPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_ls_om_preopt'

# class DeLsQtPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_ls_qt_preopt'

# class DeLsLbPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_ls_lb_preopt'

# class DeLsJdPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_ls_jd_preopt'


# # Concept: HB
# class DeHbAePreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_hb_ae_preopt'

# class DeHbBhPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_hb_bh_preopt'

# class DeHbEgPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_hb_eg_preopt'

# class DeHbKsPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_hb_ks_preopt'

# class DeHbKwPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_hb_kw_preopt'

# class DeHbOmPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_hb_om_preopt'

# class DeHbQtPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_hb_qt_preopt'

# class DeHbLbPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_hb_lb_preopt'

# class DeHbJdPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_hb_jd_preopt'


# # Concept: SP
# class DeSpAePreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_sp_ae_preopt'

# class DeSpBhPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_sp_bh_preopt'

# class DeSpEgPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_sp_eg_preopt'

# class DeSpKsPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_sp_ks_preopt'

# class DeSpKwPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_sp_kw_preopt'

# class DeSpOmPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_sp_om_preopt'

# class DeSpQtPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_sp_qt_preopt'

# class DeSpLbPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_sp_lb_preopt'

# class DeSpJdPreopt(BaseDePreoptModel):
#     class Meta:
#         managed = False
#         db_table = 'de_sp_jd_preopt'


